<x-app-layout>
    <x-slot name="title">Data Surat Masuk</x-slot>
    <!-- Simple Datatable start -->
    <div class="card-box mb-30">
        <div class="pd-20">
            <h4 class="text-blue h4">Data Surat Masuk</h4>
        </div>
        <div class="pb-20">
            <div class="h5 pd-20 mb-0">
                <x-button.primary-button class="btn btn-primary"><a href="{{ route('desa.surat-masuk.create') }}" style="text-decoration: none; color:white;"> Add Surat Masuk </a></x-button.primary-button>

                <!-- Debug buttons (remove in production) -->
                <button type="button" class="btn btn-warning ml-2" onclick="testModal()">Test Modal</button>
                <button type="button" class="btn btn-info ml-2" onclick="testDeleteModal()">Test Delete Modal</button>
            </div>
            <div class="table-responsive">
                <table class="data-table table stripe hover nowrap" style="width: 100%;">
                    <thead>
                        <tr>
                            <th class="table-plus" style="width: 5%;">#</th>
                            <th style="width: 30%;">Nama</th>
                            <th style="width: 25%;">No Surat</th>
                            <th style="width: 20%;" class="datatable-nosort">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($suratMasuk as $surat)
                        <tr>
                            <td class="table-plus">{{$loop->iteration}}</td>
                            <td>{{$surat->nama}}</td>
                            <td>{{$surat->no_surat}}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    @if($surat->file)
                                    <a href="{{ asset('storage/' . $surat->file) }}" target="_blank" class="btn btn-sm btn-success" title="Lihat PDF">
                                        <i class="fa fa-eye"></i>
                                    </a>
                                    <a href="{{ asset('storage/' . $surat->file) }}" download class="btn btn-sm btn-warning" title="Download PDF">
                                        <i class="fa fa-download"></i>
                                    </a>
                                    @else
                                    <span class="btn btn-sm btn-secondary disabled">
                                        <i class="fa fa-file-o"></i>
                                    </span>
                                    @endif
                                    <a href="{{route('desa.surat-masuk.edit', $surat->id)}}" class="btn btn-sm btn-primary" title="Edit">
                                        <i class="dw dw-edit2"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger btn-delete" title="Hapus" data-id="{{ $surat->id }}" data-nama="{{ $surat->nama }}">
                                        <i class="dw dw-delete-3"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- Simple Datatable End -->

    <!-- Modal Konfirmasi Hapus -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Konfirmasi Hapus</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin menghapus surat masuk dari <strong id="deleteNama"></strong>?</p>
                    <p class="text-danger"><small><i class="fa fa-warning"></i> Tindakan ini tidak dapat dibatalkan!</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">
                            <i class="fa fa-trash"></i> Hapus
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Global function to show delete modal
        function showDeleteModal(id, nama) {
            console.log('showDeleteModal called with ID:', id, 'Nama:', nama);

            // Set modal content
            $('#deleteNama').text(nama);
            $('#deleteForm').attr('action', '{{ route("desa.surat-masuk.destroy", ":id") }}'.replace(':id', id));

            // Show modal
            $('#deleteModal').modal('show');
        }

        $(document).ready(function() {
            console.log('Document ready'); // Debug log

            // Check if jQuery and Bootstrap are loaded
            if (typeof $ === 'undefined') {
                console.error('jQuery is not loaded!');
                return;
            }

            if (typeof $.fn.modal === 'undefined') {
                console.error('Bootstrap modal is not loaded!');
                return;
            }

            // Initialize DataTable with responsive options
            $('.data-table').DataTable({
                responsive: true,
                columnDefs: [{
                        targets: [3],
                        orderable: false
                    } // Disable sorting for action column
                ],
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json'
                }
            });

            // Handle delete button click using event delegation
            $(document).on('click', '.btn-delete', function(e) {
                e.preventDefault();
                console.log('Delete button clicked via event delegation'); // Debug log

                const id = $(this).data('id');
                const nama = $(this).data('nama');

                console.log('ID:', id, 'Nama:', nama); // Debug log

                if (!id || !nama) {
                    console.error('Missing data attributes');
                    alert('Error: Data tidak lengkap');
                    return;
                }

                showDeleteModal(id, nama);
            });

            // Direct click handler as fallback
            $('.btn-delete').on('click', function(e) {
                e.preventDefault();
                console.log('Delete button clicked via direct handler'); // Debug log

                const id = $(this).data('id');
                const nama = $(this).data('nama');

                console.log('ID:', id, 'Nama:', nama); // Debug log

                if (!id || !nama) {
                    console.error('Missing data attributes');
                    alert('Error: Data tidak lengkap');
                    return;
                }

                showDeleteModal(id, nama);
            });

            // Handle form submission with loading state
            $('#deleteForm').on('submit', function() {
                const submitBtn = $(this).find('button[type="submit"]');
                submitBtn.prop('disabled', true);
                submitBtn.html('<i class="fa fa-spinner fa-spin"></i> Menghapus...');
            });

            // Test functions for debugging
            window.testModal = function() {
                console.log('Testing modal...');
                $('#deleteModal').modal('show');
            };

            window.testDeleteModal = function() {
                showDeleteModal(1, 'Test User');
            };

            console.log('All event handlers attached'); // Debug log
        });
    </script>
    @endpush

</x-app-layout>