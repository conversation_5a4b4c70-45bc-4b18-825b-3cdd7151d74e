<x-app-layout>
    <x-slot name="title">Data Surat Masuk</x-slot>
    <!-- Simple Datatable start -->
    <div class="card-box mb-30">
        <div class="pd-20">
            <h4 class="text-blue h4">Data Surat Masuk</h4>
        </div>
        <div class="pb-20">
            <div class="h5 pd-20 mb-0">
                <x-button.primary-button class="btn btn-primary"><a href="{{ route('desa.surat-masuk.create') }}" style="text-decoration: none; color:white;"> Add Surat Masuk </a></x-button.primary-button>
            </div>
            <table class="data-table table stripe hover nowrap">
                <thead>
                    <tr>
                        <th class="table-plus">#</th>
                        <th>Nama</th>
                        <th>No Surat</th>
                        <th>PDF</th>
                        <th class="datatable-nosort">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($suratMasuk as $surat)
                    <tr>
                        <td class="table-plus">{{$loop->iteration}}</td>
                        <td>{{$surat->nama}}</td>
                        <td>{{$surat->no_surat}}</td>
                        <td>
                            @if($surat->file)
                            <a href="{{ asset('storage/' . $surat->file) }}" target="_blank" class="btn btn-sm btn-success" title="Buka PDF di Tab Baru">
                                <i class="fa fa-eye"></i>
                            </a>
                            <a href="{{ asset('storage/' . $surat->file) }}" download class="btn btn-sm btn-warning" title="Download PDF">
                                <i class="fa fa-download"></i>
                            </a>
                            @else
                            <span class="badge badge-secondary">No PDF</span>
                            @endif
                        </td>
                        <td>
                            <a href="{{route('desa.surat-masuk.edit', $surat->id)}}" class="btn btn-sm btn-primary ml-1" title="Edit">
                                <i class="dw dw-edit2"></i>
                            </a>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    <!-- Simple Datatable End -->

    @push('scripts')
    @endpush

</x-app-layout>