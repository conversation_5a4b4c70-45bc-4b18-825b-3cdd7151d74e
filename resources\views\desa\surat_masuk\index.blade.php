<x-app-layout>
    <x-slot name="title">Data Surat Masuk</x-slot>
    <!-- Simple Datatable start -->
    <div class="card-box mb-30">
        <div class="pd-20">
            <h4 class="text-blue h4">Data Surat Masuk</h4>
        </div>
        <div class="pb-20">
            <div class="h5 pd-20 mb-0">
                <x-button.primary-button class="btn btn-primary"><a href="{{ route('desa.surat-masuk.create') }}" style="text-decoration: none; color:white;"> Add Surat Masuk </a></x-button.primary-button>
            </div>
            <div class="table-responsive">
                <table class="data-table table stripe hover nowrap" style="width: 100%;">
                    <thead>
                        <tr>
                            <th class="table-plus" style="width: 5%;">#</th>
                            <th style="width: 30%;">Nama</th>
                            <th style="width: 25%;">No Surat</th>
                            <th style="width: 20%;" class="datatable-nosort">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($suratMasuk as $surat)
                        <tr>
                            <td class="table-plus">{{$loop->iteration}}</td>
                            <td>{{$surat->nama}}</td>
                            <td>{{$surat->no_surat}}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    @if($surat->file)
                                    <a href="{{ asset('storage/' . $surat->file) }}" target="_blank" class="btn btn-sm btn-success" title="Lihat PDF">
                                        <i class="fa fa-eye"></i>
                                    </a>
                                    <a href="{{ asset('storage/' . $surat->file) }}" download class="btn btn-sm btn-warning" title="Download PDF">
                                        <i class="fa fa-download"></i>
                                    </a>
                                    @else
                                    <span class="btn btn-sm btn-secondary disabled">
                                        <i class="fa fa-file-o"></i>
                                    </span>
                                    @endif
                                    <a href="{{route('desa.surat-masuk.edit', $surat->id)}}" class="btn btn-sm btn-primary" title="Edit">
                                        <i class="dw dw-edit2"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- Simple Datatable End -->

    @push('scripts')
    <script>
        $(document).ready(function() {
            // Initialize DataTable with responsive options
            $('.data-table').DataTable({
                responsive: true,
                columnDefs: [{
                        targets: [3],
                        orderable: false
                    } // Disable sorting for action column
                ],
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json'
                }
            });
        });
    </script>
    @endpush

</x-app-layout>