<?php
    $romanMonths = ['', 'I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'];
    $currentMonth = \Carbon\Carbon::now()->format('n');
    $currentYear = \Carbon\Carbon::now()->format('Y');
    $romanMonth = $romanMonths[$currentMonth];
    
    // Format nomor surat berdasarkan tipe
    if ($type === 'skd') {
        $nomorSurat = "{$index}/ DM / {$romanMonth} / {$currentYear}";
    } elseif ($type === 'sku') {
        $nomorSurat = "563/{$index}/424.233.2.10/{$currentYear}";
    } elseif ($type === 'sktm') {
        $nomorSurat = "581/{$index}/424.302.2.10/{$currentYear}";
    } elseif ($type === 'skk') {
        $nomorSurat = "474.3/{$index}/424.213.2.10/{$currentYear}";
    } else {
        $nomorSurat = "{$index}/ {$prefix ?? 'DM'} / {$romanMonth} / {$currentYear}";
    }
?>

<p style="text-align: center; margin-top: 0; padding: 0; margin-bottom: 20px;">
    Nomor <?php echo e($type === 'skd' ? ':' : '.'); ?> <?php echo e($nomorSurat); ?>

</p>
<?php /**PATH D:\Hermanto\sudes-ok\resources\views/components/nomor-surat.blade.php ENDPATH**/ ?>