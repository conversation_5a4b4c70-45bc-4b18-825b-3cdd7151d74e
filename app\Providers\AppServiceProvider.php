<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register custom Blade directive for Roman numerals
        Blade::directive('romanMonth', function ($month = null) {
            return "<?php
                \$romanMonths = ['', 'I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'];
                \$monthNumber = $month ?: \\Carbon\\Carbon::now()->format('n');
                echo \$romanMonths[\$monthNumber];
            ?>";
        });

        // Register helper function globally
        if (!function_exists('toRomanMonth')) {
            function toRomanMonth($month = null)
            {
                $romanMonths = ['', 'I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'];
                $monthNumber = $month ?: \Carbon\Carbon::now()->format('n');
                return $romanMonths[$monthNumber];
            }
        }
    }
}
