<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Buat Surat Baru <?php $__env->endSlot(); ?>
    <?php echo $__env->make('sweetalert::alert', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php if(Auth::user()->detail_users->nik == null): ?>
    <div class="alert alert-warning" role="alert">
        Lengkapi data diri anda terlebih dahulu sebelum membuat surat.
        <a href="<?php echo e(route('profile.edit')); ?>">Klik disini</a>
    </div>
    <?php elseif(Auth::user()->detail_users->status_akun == 'Pending'): ?>
    <div class="alert alert-warning" role="alert">
        Akun anda sedang ditinjau oleh staff.
    </div>
    <?php elseif(Auth::user()->detail_users->status_akun == 'Ditolak'): ?>
    <div class="alert alert-warning" role="alert">
        Akun anda tidak diperbolehkan membuat surat.
    </div>
    <?php else: ?>
    <div class="pd-20 card-box mb-30">
        <div class="clearfix">
            <div class="pull-left">
                <h4 class="text-blue h4">Buat Surat Baru</h4>
            </div>
        </div>

        <form>
            <div class="form-group row">
                <div class="col-md-12">

                    <select name="state" style="width: 100%; height: 38px" onchange="showCard(this.value)">
                        <option value="">Pilih Jenis Surat</option>
                        <option value="skd">Surat Keterangan Domisili (SKD)</option>
                        <option value="skk">Surat Keterangan Kematian (SKK)</option>
                        <option value="sktm">Surat Keterangan Tidak Mampu (SKTM)</option>
                        <option value="sku">Surat Keterangan Usaha (SKU)</option>
                    </select>

                </div>
            </div>
        </form>
    </div>
    <?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal5370f2749d4c0dc39bd9a15ddfc2d3da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5370f2749d4c0dc39bd9a15ddfc2d3da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form-surat.skd','data' => ['id' => 'skd','detailSurat' => $detailSurat]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form-surat.skd'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'skd','detailSurat' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($detailSurat)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5370f2749d4c0dc39bd9a15ddfc2d3da)): ?>
<?php $attributes = $__attributesOriginal5370f2749d4c0dc39bd9a15ddfc2d3da; ?>
<?php unset($__attributesOriginal5370f2749d4c0dc39bd9a15ddfc2d3da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5370f2749d4c0dc39bd9a15ddfc2d3da)): ?>
<?php $component = $__componentOriginal5370f2749d4c0dc39bd9a15ddfc2d3da; ?>
<?php unset($__componentOriginal5370f2749d4c0dc39bd9a15ddfc2d3da); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginal7e8d487feef4179ca0efca9bc110e212 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7e8d487feef4179ca0efca9bc110e212 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form-surat.skk','data' => ['id' => 'skk','detailSurat' => $detailSurat]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form-surat.skk'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'skk','detailSurat' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($detailSurat)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7e8d487feef4179ca0efca9bc110e212)): ?>
<?php $attributes = $__attributesOriginal7e8d487feef4179ca0efca9bc110e212; ?>
<?php unset($__attributesOriginal7e8d487feef4179ca0efca9bc110e212); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7e8d487feef4179ca0efca9bc110e212)): ?>
<?php $component = $__componentOriginal7e8d487feef4179ca0efca9bc110e212; ?>
<?php unset($__componentOriginal7e8d487feef4179ca0efca9bc110e212); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginal26bf51d9b38dcbfa3109685273554e2e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal26bf51d9b38dcbfa3109685273554e2e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form-surat.sktm','data' => ['id' => 'sktm','detailSurat' => $detailSurat]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form-surat.sktm'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'sktm','detailSurat' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($detailSurat)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal26bf51d9b38dcbfa3109685273554e2e)): ?>
<?php $attributes = $__attributesOriginal26bf51d9b38dcbfa3109685273554e2e; ?>
<?php unset($__attributesOriginal26bf51d9b38dcbfa3109685273554e2e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal26bf51d9b38dcbfa3109685273554e2e)): ?>
<?php $component = $__componentOriginal26bf51d9b38dcbfa3109685273554e2e; ?>
<?php unset($__componentOriginal26bf51d9b38dcbfa3109685273554e2e); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginal597cbcce1a84675e4892ddb4a22c1cf3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal597cbcce1a84675e4892ddb4a22c1cf3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form-surat.sku','data' => ['id' => 'sku','detailSurat' => $detailSurat,'warga' => $warga]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form-surat.sku'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'sku','detailSurat' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($detailSurat),'warga' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($warga)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal597cbcce1a84675e4892ddb4a22c1cf3)): ?>
<?php $attributes = $__attributesOriginal597cbcce1a84675e4892ddb4a22c1cf3; ?>
<?php unset($__attributesOriginal597cbcce1a84675e4892ddb4a22c1cf3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal597cbcce1a84675e4892ddb4a22c1cf3)): ?>
<?php $component = $__componentOriginal597cbcce1a84675e4892ddb4a22c1cf3; ?>
<?php unset($__componentOriginal597cbcce1a84675e4892ddb4a22c1cf3); ?>
<?php endif; ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>

<script>
    function showCard(selectedValue) {
        // Semua card disembunyikan terlebih dahulu
        document.getElementById("skd").style.display = "none";
        document.getElementById("skk").style.display = "none";
        document.getElementById("sktm").style.display = "none";
        document.getElementById("sku").style.display = "none";

        // Tampilkan card sesuai dengan nilai yang dipilih
        if (selectedValue === "skd") {
            document.getElementById("skd").style.display = "block";
        } else if (selectedValue === "skk") {
            document.getElementById("skk").style.display = "block";
        } else if (selectedValue === "sktm") {
            document.getElementById("sktm").style.display = "block";
        } else if (selectedValue === "sku") {
            document.getElementById("sku").style.display = "block";
        }
    }
</script><?php /**PATH D:\Hermanto\sudes-ok\resources\views/desa/surat/index.blade.php ENDPATH**/ ?>