<div class="sidebar-menu">
    <ul id="accordion-menu">
        <?php if(auth()->user()->hasRole('admin')): ?>
        
        <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('admin/users') ? 'active' : '').'','link' => ''.e(route('admin.users.index')).'','icon' => 'micon bi bi-archive','title' => 'Users']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('admin/users') ? 'active' : '').'','link' => ''.e(route('admin.users.index')).'','icon' => 'micon bi bi-archive','title' => 'Users']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
        <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('pengguna-baru') ? 'active' : '').'','link' => ''.e(route('pengguna-baru')).'','icon' => 'micon bi bi-person-plus','title' => 'Pengguna Baru']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('pengguna-baru') ? 'active' : '').'','link' => ''.e(route('pengguna-baru')).'','icon' => 'micon bi bi-person-plus','title' => 'Pengguna Baru']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>

        <?php elseif(auth()->user()->hasRole('kades')): ?>
        <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('kades') ? 'active' : '').'','link' => ''.e(route('kades.dashboard')).'','icon' => 'micon bi bi-house','title' => 'Home']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('kades') ? 'active' : '').'','link' => ''.e(route('kades.dashboard')).'','icon' => 'micon bi bi-house','title' => 'Home']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
        <li class="dropdown">
            <a href="javascript:;" class="dropdown-toggle <?php echo e(request()->is('kades/*') ? 'active' : ''); ?>">
                <span class="micon bi bi-file-earmark-text"></span><span class="mtext">Pengajuan</span>
            </a>
            <ul class="submenu">
                <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('kades/pengajuan') ? 'active' : '').'','link' => ''.e(route('kades.pengajuan.index')).'','icon' => '','title' => 'Belum di Tanda Tangani']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('kades/pengajuan') ? 'active' : '').'','link' => ''.e(route('kades.pengajuan.index')).'','icon' => '','title' => 'Belum di Tanda Tangani']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('kades/list') ? 'active' : '').'','link' => ''.e(route('kades.pengajuan.list')).'','icon' => '','title' => 'Sudah di Tanda Tangani']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('kades/list') ? 'active' : '').'','link' => ''.e(route('kades.pengajuan.list')).'','icon' => '','title' => 'Sudah di Tanda Tangani']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('kades/reject') ? 'active' : '').'','link' => ''.e(route('kades.pengajuan.reject')).'','icon' => '','title' => 'Ditolak']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('kades/reject') ? 'active' : '').'','link' => ''.e(route('kades.pengajuan.reject')).'','icon' => '','title' => 'Ditolak']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
                
            </ul>
        </li>

        <?php elseif(auth()->user()->hasRole('staff')): ?>
        <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('staff') ? 'active' : '').'','link' => ''.e(route('staff.dashboard')).'','icon' => 'micon bi bi-house','title' => 'Home']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('staff') ? 'active' : '').'','link' => ''.e(route('staff.dashboard')).'','icon' => 'micon bi bi-house','title' => 'Home']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
        <li class="dropdown">
            <a href="javascript:;" class="dropdown-toggle <?php echo e(request()->is('staff/*') ? 'active' : ''); ?>">
                <span class="micon bi bi-file-earmark-text"></span><span class="mtext">Pengajuan</span>
            </a>
            <ul class="submenu">
                <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('staff/pengajuan') ? 'active' : '').'','link' => ''.e(route('staff.pengajuan.index')).'','icon' => '','title' => 'Belum diKonfirmasi']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('staff/pengajuan') ? 'active' : '').'','link' => ''.e(route('staff.pengajuan.index')).'','icon' => '','title' => 'Belum diKonfirmasi']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('staff/list') ? 'active' : '').'','link' => ''.e(route('staff.pengajuan.list')).'','icon' => '','title' => 'Sudah dikonfirmasi']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('staff/list') ? 'active' : '').'','link' => ''.e(route('staff.pengajuan.list')).'','icon' => '','title' => 'Sudah dikonfirmasi']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
                
            </ul>
        </li>
        <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('pengguna-baru') ? 'active' : '').'','link' => ''.e(route('pengguna-baru')).'','icon' => 'micon bi bi-person-plus','title' => 'Pengguna Baru']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('pengguna-baru') ? 'active' : '').'','link' => ''.e(route('pengguna-baru')).'','icon' => 'micon bi bi-person-plus','title' => 'Pengguna Baru']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>

        <?php elseif(auth()->user()->hasRole('desa')): ?>
        
        <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('desa') ? 'active' : '').'','link' => ''.e(route('desa.dashboard')).'','icon' => 'micon bi bi-house','title' => 'Home']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('desa') ? 'active' : '').'','link' => ''.e(route('desa.dashboard')).'','icon' => 'micon bi bi-house','title' => 'Home']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
        <li class="dropdown">
            <a href="javascript:;" class="dropdown-toggle <?php echo e(request()->is('desa/surat*') ? 'active' : ''); ?>">
                <span class="micon bi bi-file-earmark-text"></span><span class="mtext">Surat</span>
            </a>
            <ul class="submenu">
                <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('desa/surat') ? 'active' : '').'','link' => ''.e(route('desa.surat.index')).'','icon' => '','title' => 'Buat Surat Baru']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('desa/surat') ? 'active' : '').'','link' => ''.e(route('desa.surat.index')).'','icon' => '','title' => 'Buat Surat Baru']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
                
                <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('desa/surat/riwayat') ? 'active' : '').'','link' => ''.e(route('desa.surat.riwayat')).'','icon' => '','title' => 'Riwayat Pengajuan']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('desa/surat/riwayat') ? 'active' : '').'','link' => ''.e(route('desa.surat.riwayat')).'','icon' => '','title' => 'Riwayat Pengajuan']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
            </ul>
        </li>
        <!-- <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('desa') ? 'active' : '').'','link' => ''.e(route('desa.dashboard')).'','icon' => 'micon bi bi-file-earmark-text','title' => 'Surat Masuk']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('desa') ? 'active' : '').'','link' => ''.e(route('desa.dashboard')).'','icon' => 'micon bi bi-file-earmark-text','title' => 'Surat Masuk']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?> -->
        <!-- <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('desa/warga') ? 'active' : '').'','link' => ''.e(route('desa.warga.index')).'','icon' => 'micon bi bi-file-earmark-text','title' => 'Data Warga']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('desa/warga') ? 'active' : '').'','link' => ''.e(route('desa.warga.index')).'','icon' => 'micon bi bi-file-earmark-text','title' => 'Data Warga']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?> -->
        <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('desa/warga*') ? 'active' : '').'','link' => ''.e(route('desa.warga.index')).'','icon' => 'micon bi bi-file-earmark-text','title' => 'Data Warga']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('desa/warga*') ? 'active' : '').'','link' => ''.e(route('desa.warga.index')).'','icon' => 'micon bi bi-file-earmark-text','title' => 'Data Warga']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
        <?php if (isset($component)) { $__componentOriginal261d69dc02edab27c5562fdc4f2c2254 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.menu.sidebar-menu-item','data' => ['class' => 'no-arrow '.e(request()->is('desa/surat-masuk*') ? 'active' : '').'','link' => ''.e(route('desa.surat-masuk.index')).'','icon' => 'micon bi bi-file-earmark-text','title' => 'Surat Masuk']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('menu.sidebar-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'no-arrow '.e(request()->is('desa/surat-masuk*') ? 'active' : '').'','link' => ''.e(route('desa.surat-masuk.index')).'','icon' => 'micon bi bi-file-earmark-text','title' => 'Surat Masuk']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $attributes = $__attributesOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__attributesOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254)): ?>
<?php $component = $__componentOriginal261d69dc02edab27c5562fdc4f2c2254; ?>
<?php unset($__componentOriginal261d69dc02edab27c5562fdc4f2c2254); ?>
<?php endif; ?>
        <?php endif; ?>
    </ul>
</div><?php /**PATH D:\Hermanto\sudes-ok\resources\views/components/sidebar/sidebar-menu.blade.php ENDPATH**/ ?>