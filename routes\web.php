<?php

use App\Http\Controllers\AdminDashboard\AdminController;
use App\Http\Controllers\AdminDashboard\UserController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\FrontController;
use App\Http\Controllers\KadesDashboard\KadesController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\StaffDashboard\StaffController;
use App\Http\Controllers\DesaDashboard\SuratController;
use App\Http\Controllers\DesaDashboard\DesaController;
use App\Http\Controllers\DesaDashboard\SuratMasukController;
use App\Http\Controllers\DesaDashboard\WargaController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', DashboardController::class)->middleware(['auth', 'verified'])->name('dashboard');
Route::get('/cek/surat/{id}', [FrontController::class, 'cek'])->name('cek.surat');
Route::get('/download/surat/{id}', [FrontController::class, 'unduh'])->name('unduh.surat');
Route::get('/preview/surat', [FrontController::class, 'preview'])->name('preview.surat');
Route::get('/qr', [FrontController::class, 'qr'])->name('qr');

// Route::middleware('auth')->group(function () {
//     Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
//     Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
//     Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
// });
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

//kades
Route::middleware(['auth', 'verified', 'role:kades'])->group(function () {
    Route::get('kades', [KadesController::class, 'dashboard'])->name('kades.dashboard');
    Route::get('kades/list', [KadesController::class, 'list'])->name('kades.pengajuan.list');
    Route::get('kades/reject', [KadesController::class, 'reject'])->name('kades.pengajuan.reject');
    Route::get('kades/berkas/{id}', [KadesController::class, 'berkas'])->name('kades.pengajuan.berkas');
    Route::put('kades/pengajuan/acc/{id}', [KadesController::class, 'acc'])->name('kades.pengajuan.acc');
    Route::put('kades/pengajuan/rej/{id}', [KadesController::class, 'rej'])->name('kades.pengajuan.rej');
    Route::post('kades/ttd/{id}', [KadesController::class, 'ttd'])->name('kades.pengajuan.ttd');
    Route::post('kades/tolak/{id}', [KadesController::class, 'tolak'])->name('kades.pengajuan.tolak');
    Route::resource('kades/pengajuan', KadesController::class)->names([
        'index' => 'kades.pengajuan.index',
        'create' => 'kades.pengajuan.create',
        'store' => 'kades.pengajuan.store',
        'show' => 'kades.pengajuan.show',
        'edit' => 'kades.pengajuan.edit',
        'update' => 'kades.pengajuan.update',
        'destroy' => 'kades.pengajuan.destroy'
    ]);
});

// Admin
Route::middleware(['auth', 'verified', 'role:admin'])->group(function () {
    Route::get('admin', AdminController::class)->name('admin.dashboard');
    Route::resource('admin/users', UserController::class)->names([
        'index' => 'admin.users.index',
        'create' => 'admin.users.create',
        'store' => 'admin.users.store',
        'show' => 'admin.users.show',
        'edit' => 'admin.users.edit',
        'update' => 'admin.users.update',
        'destroy' => 'admin.users.destroy'
    ]);
});

Route::middleware(['auth', 'verified', 'role:staff'])->group(function () {
    Route::get('staff', [StaffController::class, 'dashboard'])->name('staff.dashboard');
    Route::get('staff/list', [StaffController::class, 'list'])->name('staff.pengajuan.list');
    Route::get('staff/berkas/{id}', [StaffController::class, 'berkas'])->name('staff.pengajuan.berkas');
    Route::put('staff/pengajuan/{id}', [StaffController::class, 'confirm'])->name('staff.pengajuan.confirm');
    Route::resource('staff/pengajuan', StaffController::class)->names([
        'index' => 'staff.pengajuan.index',
        'create' => 'staff.pengajuan.create',
        'store' => 'staff.pengajuan.store',
        'show' => 'staff.pengajuan.show',
        'edit' => 'staff.pengajuan.edit',
        'update' => 'staff.pengajuan.update',
        'destroy' => 'staff.pengajuan.destroy'
    ]);
});

// Desa
Route::middleware(['auth', 'verified', 'role:desa'])->group(function () {
    Route::get('desa', DesaController::class)->name('desa.dashboard');
    Route::get('desa/surat/berkas/{id}', [SuratController::class, 'berkas'])->name('desa.surat.berkas');
    Route::post('desa/surat/send/{id}', [SuratController::class, 'send'])->name('desa.surat.send');
    Route::get('desa/surat/draft', [SuratController::class, 'draft'])->name('desa.surat.draft');
    Route::get('desa/surat/riwayat', [SuratController::class, 'riwayat'])->name('desa.surat.riwayat');
    Route::resource('desa/surat', SuratController::class)->names([
        'index' => 'desa.surat.index',
        'create' => 'desa.surat.create',
        'store' => 'desa.surat.store',
        'show' => 'desa.surat.show',
        'edit' => 'desa.surat.edit',
        'update' => 'desa.surat.update',
        'destroy' => 'desa.surat.destroy'
    ]);
    Route::get('/desa/get-warga-data/{nik}', [DesaController::class, 'getWargaData'])->name('desa.getWargaData');
    Route::resource('desa/warga', WargaController::class)->names([
        'index' => 'desa.warga.index',
        'create' => 'desa.warga.create',
        'store' => 'desa.warga.store',
        'show' => 'desa.warga.show',
        'edit' => 'desa.warga.edit',
        'update' => 'desa.warga.update',
        'destroy' => 'desa.warga.destroy'
    ]);
    Route::get('desa/surat/pdf/{id}', [SuratController::class, 'pdf'])->name('desa.surat.pdf');
    Route::resource('desa/surat-masuk', SuratMasukController::class)->names([
        'index' => 'desa.surat-masuk.index',
        'create' => 'desa.surat-masuk.create',
        'store' => 'desa.surat-masuk.store',
        'show' => 'desa.surat-masuk.show',
        'edit' => 'desa.surat-masuk.edit',
        'update' => 'desa.surat-masuk.update',
        'destroy' => 'desa.surat-masuk.destroy'
    ]);
});

Route::middleware(['auth', 'verified', 'role:staff|admin'])->group(function () {
    Route::get('/pengguna-baru', [UserController::class, 'baru'])->name('pengguna-baru');
    Route::get('/pengguna-baru/{id}', [UserController::class, 'baru_detail'])->name('pengguna-baru.detail');
    Route::post('/pengguna-baru/{id}', [UserController::class, 'baru_konfirmasi'])->name('pengguna-baru.konfirmasi');
    Route::post('/pengguna-baru/tolak/{id}', [UserController::class, 'baru_tolak'])->name('pengguna-baru.tolak');
});

require __DIR__ . '/auth.php';
