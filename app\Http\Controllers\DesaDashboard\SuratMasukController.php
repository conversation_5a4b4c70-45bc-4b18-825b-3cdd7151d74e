<?php

namespace App\Http\Controllers\DesaDashboard;

use App\Http\Controllers\Controller;
use App\Models\SuratMasuk;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use RealRashid\SweetAlert\Facades\Alert;

class SuratMasukController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    public function index()
    {
        $suratMasuk = SuratMasuk::latest()->get();

        return view('desa.surat_masuk.index', compact('suratMasuk'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('desa.surat_masuk.form', [
            'suratMasuk' => new SuratMasuk(),
            'metapage' => [
                'title' => 'Tambah Surat Baru',
                'url' => route('desa.surat-masuk.store'),
                'method' => 'POST',
                'button' => 'Add'
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // dd($request->all());
        $data = [
            'nama' => $request->nama,
            'no_surat' => $request->no_surat,
            'file' => $request->file('file')->store('assets/surat_masuk', 'public'),
        ];

        $surat = SuratMasuk::create($data);

        Alert::success('Sukses!', 'Data Berhasil Dibuat');
        return redirect()->route('desa.surat-masuk.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id) {}

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $detailSurat = SuratMasuk::where('id', $id)->first();
        return view('desa.surat_masuk.edit', compact('detailSurat'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $detailSurat = SuratMasuk::where('id', $id)->first();
        $get_file = $detailSurat->file;
        // dd($get_file);
        if (isset($request->file)) {
            $data = 'storage/' . $get_file;
            if (File::exists($data)) {
                File::delete($data);
            } else {
                File::delete('storage/app/public/' . $get_file);
            }
        }

        SuratMasuk::where('id', $id)->update([
            'nama' => $request->nama,
            'no_surat' => $request->no_surat,
            'file' => $request->hasFile('file') ? $request->file('file')->store('assets/surat_masuk', 'public') : $detailSurat->file,
        ]);

        Alert::success('Sukses!', 'Data Berhasil DiEdit');
        return redirect()->route('desa.surat-masuk.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            // Find surat masuk by ID
            $suratMasuk = SuratMasuk::findOrFail($id);

            // Delete file if exists
            if ($suratMasuk->file) {
                $filePath = 'storage/' . $suratMasuk->file;
                if (File::exists($filePath)) {
                    File::delete($filePath);
                } else {
                    File::delete('storage/app/public/' . $suratMasuk->file);
                }
            }

            // Delete record from database
            $suratMasuk->delete();

            Alert::success('Sukses!', 'Surat Masuk Berhasil Dihapus');
            return redirect()->route('desa.surat-masuk.index');
        } catch (\Exception $e) {
            Alert::error('Error!', 'Gagal menghapus surat masuk: ' . $e->getMessage());
            return redirect()->route('desa.surat-masuk.index');
        }
    }
}
