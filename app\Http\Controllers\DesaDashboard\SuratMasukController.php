<?php

namespace App\Http\Controllers\DesaDashboard;

use App\Http\Controllers\Controller;
use App\Models\SuratMasuk;
use Illuminate\Http\Request;
use RealRashid\SweetAlert\Facades\Alert;

class SuratMasukController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    public function index()
    {
        $suratMasuk = SuratMasuk::latest()->get();

        return view('desa.surat_masuk.index', compact('suratMasuk'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('desa.surat_masuk.form', [
            'suratMasuk' => new SuratMasuk(),
            'metapage' => [
                'title' => 'Tambah Surat Baru',
                'url' => route('desa.surat-masuk.store'),
                'method' => 'POST',
                'button' => 'Create'
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // dd($request->all());
        $data = [
            'nama' => $request->name,
            'nik' => $request->nik,
            'jenis_kelamin' => $request->gender,
            'tempat_lahir' => $request->born_place,
            'tgl_lahir' => $request->born_date,
            'agama' => $request->religion,
            'kewarganegaraan' => $request->kewarganegaraan,
            'pekerjaan' => $request->pekerjaan,
            'status_pernikahan' => $request->status_pernikahan,
            'desa' => $request->dusun,
            'rt' => $request->rt,
            'rw' => $request->rw,
        ];

        $surat = SuratMasuk::create($data);

        Alert::success('Sukses!', 'Data Berhasil Dibuat');
        return redirect()->route('desa.warga.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id) {}

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $detailWarga = DataWarga::where('id', $id)->first();
        // dd($detailWarga);
        return view('desa.warga.edit', compact('detailWarga'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // $detailWarga = DataWarga::where('id', $id)->first();

        DataWarga::where('id', $id)->update([
            'nama' => $request->nama,
            'nik' => $request->nik,
            'jenis_kelamin' => $request->jenis_kelamin,
            'tempat_lahir' => $request->tempat_lahir,
            'tgl_lahir' => $request->tgl_lahir,
            'agama' => $request->agama,
            'kewarganegaraan' => $request->kewarganegaraan,
            'pekerjaan' => $request->pekerjaan,
            'status_pernikahan' => $request->status_pernikahan,
            'desa' => $request->desa,
            'rt' => $request->rt,
            'rw' => $request->rw,
        ]);

        Alert::success('Sukses!', 'Data Berhasil DiEdit');
        return redirect()->route('desa.warga.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $pengajuanSurat = PengajuanSurat::where('id', $id)->first();
        $detailSurat = DetailSurat::where('pengajuan_surat_id', $id)->first();
        $get_berkas = $detailSurat->berkas;
        $data = 'storage/' . $get_berkas;
        if (File::exists($data)) {
            File::delete($data);
        } else {
            File::delete('storage/app/public/' . $get_berkas);
        }
        $pengajuanSurat->delete();
        $detailSurat->delete();
        Alert::success('Sukses!', 'Surat Berhasil Dihapus');
        return redirect()->route('desa.surat.riwayat');
    }
}
