<style>
    .select2-container {
        z-index: 9999;
        width: 100% !important;
    }
</style>
<div {{$attributes}} style="display: none;" class="pd-20 card-box mb-30">
    <form method="POST" action="{{ route('desa.surat.store') }}" enctype="multipart/form-data">
        @csrf
        <x-text-input value="sku" name="jenis_surat" type="text" hidden />

        <div class="clearfix">
            <h4 class="text-blue h4">Surat Keterangan Usaha</h4>
        </div>

        <div class="wizard-content">
            <h6>Data pemilik usaha :</h6>
            <section>
                <div class="row">

                    <!-- <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>Nama : </x-input-label>
                            <x-text-input name="nama" type="text" class="form-control" />
                            <x-input-error class="mt-2" :messages="$errors->get('nama')" />
                        </div>
                    </div> -->

                    <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>NIK : </x-input-label>
                            <select class="custom-select2 form-control" name="nik" id="nik-select">
                                <option value="">Pilih NIK</option>
                                @foreach($warga as $value)
                                @if($value && $value->nik)
                                <option value="{{ $value->nik }}">{{ $value->nik }} - {{ $value->nama }}</option>
                                @endif
                                @endforeach
                            </select>
                            <x-input-error class="mt-2" :messages="$errors->get('nik')" />
                        </div>
                    </div>

                    <!-- <div class="col-md-6">
                        <div class="form-group">
                            <label>Jenis Kelamin :</label>
                            <select name="gender" class="form-control">
                                <option>Pilih Jenis Kelamin</option>
                                <option value="Laki - Laki">Laki - laki</option>
                                <option value="Perempuan">Perempuan</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>Tempat Lahir : </x-input-label>
                            <x-text-input name="tempat_lahir" type="text" class="form-control" />
                            <x-input-error class="mt-2" :messages="$errors->get('tempat_lahir')" />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>Tanggal Lahir : </x-input-label>
                            <x-text-input name="tanggal_lahir" type="date" class="form-control" />
                            <x-input-error class="mt-2" :messages="$errors->get('tanggal_lahir')" />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>Agama : </x-input-label>
                            <select name="agama" class="form-control">
                                <option>Pilih Agama</option>
                                <option value="Islam">Islam</option>
                                <option value="Kristen">Kristen</option>
                                <option value="Hindu">Hindu</option>
                                <option value="Budha">Budha</option>
                                <option value="Konghucu">Konghucu</option>
                            </select>
                            <x-input-error class="mt-2" :messages="$errors->get('agama')" />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>Kewarganegaraan : </x-input-label>
                            <x-text-input name="kewarganegaraan" type="text" class="form-control" />
                            <x-input-error class="mt-2" :messages="$errors->get('kewarganegaraan')" />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Status Pernikahan :</label>
                            <select name="status_pernikahan" class="form-control">
                                <option>Pilih Status Pernikahan</option>
                                <option value="Belum Menikah">Belum Menikah</option>
                                <option value="Menikah">Menikah</option>
                                <option value="Pernah Menikah">Pernah Menikah</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>Pekerjaan : </x-input-label>
                            <x-text-input name="pekerjaan" type="text" class="form-control" />
                            <x-input-error class="mt-2" :messages="$errors->get('pekerjaan')" />
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Dusun</label>
                            <select name="dusun" class="form-control">
                                <option>Pilih Dusun</option>
                                <option value="Manyampa Barat">Manyampa Barat</option>
                                <option value="Manyampa Timur">Manyampa Timur</option>
                                <option value="Manyampa Utara">Manyampa Utara</option>
                                <option value="Manyampa Selatan">Manyampa Selatan</option>
                                <option value="Manyampa Tengah">Manyampa Tengah</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-1">
                        <div class="form-group">
                            <label>RT</label>
                            <select name="rt" class="form-control">
                                <option>RT</option>
                                @for ($i = 1; $i <= 23; $i++)
                                    <option value="{{$i}}">{{$i}}</option>
                                    @endfor
                            </select>
                        </div>
                    </div>

                    <div class="col-md-1">
                        <div class="form-group">
                            <label>RW</label>
                            <select name="rw" class="form-control">
                                <option>RW</option>
                                @for ($i = 1; $i <= 10; $i++)
                                    <option value="{{$i}}">{{$i}}</option>
                                    @endfor
                            </select>
                        </div>
                    </div> -->

                    <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>Berkas Persyaratan (.zip / .rar) : [[ <a data-toggle="modal" data-target="#passwordModal4" href="#">Lihat Syarat</a> ]]</x-input-label>
                            <x-text-input name="berkas" type="file" class="form-control" />
                            <x-input-error class="mt-2" :messages="$errors->get('berkas')" />
                        </div>
                    </div>

                </div>
            </section>

            <h6>Keterangan usaha :</h6>
            <section>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>Nama Usaha : </x-input-label>
                            <x-text-input name="nama_instansi" type="text" class="form-control" />
                            <x-input-error class="mt-2" :messages="$errors->get('nama_instansi')" />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>Mulai Usaha : </x-input-label>
                            <x-text-input name="mulai_usaha" type="date" class="form-control" />
                            <x-input-error class="mt-2" :messages="$errors->get('mulai_usaha')" />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>Alamat Usaha : </x-input-label>
                            <x-text-input name="alamat_usaha" type="text" class="form-control" />
                            <x-input-error class="mt-2" :messages="$errors->get('alamat_usaha')" />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <x-input-label>Tujuan : </x-input-label>
                            <x-text-input name="tujuan" type="text" class="form-control" />
                            <x-input-error class="mt-2" :messages="$errors->get('tujuan')" />
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <x-button.primary-button>Submit</x-button.primary-button>
                    </div>
                </div>
            </section>

            <div class="modal fade" id="passwordModal4" tabindex="-1" role="dialog" aria-labelledby="passwordModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="passwordModalLabel">Berkas Persyaratan</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            - Kartu Tanda Penduduk
                            <br>
                            - Tanda Lunas PBB
                            <br>
                            - Kartu Keluarga
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>

@push('scripts')
<script>
    $(document).ready(function() {

        // Event onclick untuk select2 NIK - Auto fill dengan NIK pertama
        $('#nik-select').on('click', function() {
            console.log('NIK select clicked');

            // Ambil NIK pertama yang tersedia (bukan yang kosong)
            var firstNik = $('#nik-select option:not([value=""]):first').val();

            if (firstNik) {
                console.log('Auto-filling with first NIK:', firstNik);

                // Set nilai select ke NIK pertama
                $(this).val(firstNik).trigger('change');

                // Panggil AJAX untuk mengisi form
                var url = "{{ route('desa.getWargaData', ['nik' => ':nik']) }}";
                url = url.replace(':nik', firstNik);

                $.ajax({
                    url: url,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        console.log('Auto-fill data received:', data);
                        if (data) {
                            // Populate form fields
                            $('input[name="nama"]').val(data.nama);
                            $('select[name="gender"]').val(data.jenis_kelamin).trigger('change');
                            $('input[name="tempat_lahir"]').val(data.tempat_lahir);
                            $('input[name="tanggal_lahir"]').val(data.tgl_lahir);
                            $('select[name="agama"]').val(data.agama).trigger('change');
                            $('input[name="kewarganegaraan"]').val(data.kewarganegaraan);
                            $('input[name="pekerjaan"]').val(data.pekerjaan);
                            $('select[name="dusun"]').val(data.desa).trigger('change');
                            $('select[name="rt"]').val(data.rt).trigger('change');
                            $('select[name="rw"]').val(data.rw).trigger('change');
                            $('select[name="status_pernikahan"]').val(data.status_pernikahan).trigger('change');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Auto-fill AJAX Error:', status, error);
                    }
                });
            } else {
                console.log('No NIK available for auto-fill');
            }
        });

        // Event onchange untuk select2 NIK (existing functionality)
        $('#nik-select').on('change', function() {
            var nik = $(this).val();
            console.log('Selected NIK:', nik); // Debug: Check if NIK is correctly captured

            if (nik) {
                // Construct the URL using Laravel's route helper.
                // Ensure this route exists and is accessible.
                var url = "{{ route('desa.getWargaData', ['nik' => ':nik']) }}";
                url = url.replace(':nik', nik);
                console.log('AJAX URL:', url); // Debug: Check the final URL

                $.ajax({
                    url: url,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        console.log('Data received:', data); // Debug: See the data from backend
                        if (data) {
                            // Populate form fields
                            $('input[name="nama"]').val(data.nama);
                            // For select elements, you might need to re-initialize Select2 if it's applied
                            $('select[name="gender"]').val(data.jenis_kelamin).trigger('change');
                            $('input[name="tempat_lahir"]').val(data.tempat_lahir);
                            $('input[name="tanggal_lahir"]').val(data.tgl_lahir);
                            $('select[name="agama"]').val(data.agama).trigger('change');
                            $('input[name="kewarganegaraan"]').val(data.kewarganegaraan);
                            $('input[name="pekerjaan"]').val(data.pekerjaan);
                            $('select[name="dusun"]').val(data.desa).trigger('change');
                            $('select[name="rt"]').val(data.rt).trigger('change');
                            $('select[name="rw"]').val(data.rw).trigger('change');
                            $('select[name="status_pernikahan"]').val(data.status_pernikahan).trigger('change'); // Added this line, as it was missing
                        } else {
                            console.log('No data found for this NIK.');
                            clearFormFields();
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', status, error);
                        console.error('Response Text:', xhr.responseText);
                        alert('Error fetching warga data. Check console for details.');
                    }
                });
            } else {
                console.log('NIK is empty, clearing fields.');
                clearFormFields();
            }
        });

        // Function to clear all relevant form fields
        function clearFormFields() {
            $('input[name="nama"]').val('');
            $('select[name="gender"]').val('').trigger('change');
            $('input[name="tempat_lahir"]').val('');
            $('input[name="tanggal_lahir"]').val('');
            $('select[name="agama"]').val('').trigger('change');
            $('input[name="kewarganegaraan"]').val('');
            $('input[name="pekerjaan"]').val('');
            $('select[name="status_pernikahan"]').val('').trigger('change'); // Make sure to clear this too
            $('select[name="dusun"]').val('').trigger('change');
            $('select[name="rt"]').val('').trigger('change');
            $('select[name="rw"]').val('').trigger('change');
        }
    });
</script>
@endpush