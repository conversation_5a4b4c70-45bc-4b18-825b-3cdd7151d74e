<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Data Surat Masuk <?php $__env->endSlot(); ?>
    <!-- Simple Datatable start -->
    <div class="card-box mb-30">
        <div class="pd-20">
            <h4 class="text-blue h4">Data Surat Masuk</h4>
        </div>
        <div class="pb-20">
            <div class="h5 pd-20 mb-0">
                <?php if (isset($component)) { $__componentOriginal5fc1f9c1304bf75cac6bd0db1c43f54b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5fc1f9c1304bf75cac6bd0db1c43f54b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.primary-button','data' => ['class' => 'btn btn-primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button.primary-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'btn btn-primary']); ?><a href="<?php echo e(route('desa.surat-masuk.create')); ?>" style="text-decoration: none; color:white;"> Add Surat Masuk </a> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5fc1f9c1304bf75cac6bd0db1c43f54b)): ?>
<?php $attributes = $__attributesOriginal5fc1f9c1304bf75cac6bd0db1c43f54b; ?>
<?php unset($__attributesOriginal5fc1f9c1304bf75cac6bd0db1c43f54b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5fc1f9c1304bf75cac6bd0db1c43f54b)): ?>
<?php $component = $__componentOriginal5fc1f9c1304bf75cac6bd0db1c43f54b; ?>
<?php unset($__componentOriginal5fc1f9c1304bf75cac6bd0db1c43f54b); ?>
<?php endif; ?>

                <!-- Debug buttons (remove in production) -->
                <button type="button" class="btn btn-warning ml-2" onclick="testModal()">Test Modal</button>
                <button type="button" class="btn btn-info ml-2" onclick="testDeleteModal()">Test Delete Modal</button>
            </div>
            <div class="table-responsive">
                <table class="data-table table stripe hover nowrap" style="width: 100%;">
                    <thead>
                        <tr>
                            <th class="table-plus" style="width: 5%;">#</th>
                            <th style="width: 30%;">Nama</th>
                            <th style="width: 25%;">No Surat</th>
                            <th style="width: 20%;" class="datatable-nosort">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $suratMasuk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $surat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td class="table-plus"><?php echo e($loop->iteration); ?></td>
                            <td><?php echo e($surat->nama); ?></td>
                            <td><?php echo e($surat->no_surat); ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <?php if($surat->file): ?>
                                    <a href="<?php echo e(asset('storage/' . $surat->file)); ?>" target="_blank" class="btn btn-sm btn-success" title="Lihat PDF">
                                        <i class="fa fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(asset('storage/' . $surat->file)); ?>" download class="btn btn-sm btn-warning" title="Download PDF">
                                        <i class="fa fa-download"></i>
                                    </a>
                                    <?php else: ?>
                                    <span class="btn btn-sm btn-secondary disabled">
                                        <i class="fa fa-file-o"></i>
                                    </span>
                                    <?php endif; ?>
                                    <a href="<?php echo e(route('desa.surat-masuk.edit', $surat->id)); ?>" class="btn btn-sm btn-primary" title="Edit">
                                        <i class="dw dw-edit2"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger btn-delete" title="Hapus" data-id="<?php echo e($surat->id); ?>" data-nama="<?php echo e($surat->nama); ?>">
                                        <i class="dw dw-delete-3"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- Simple Datatable End -->

    <!-- Modal Konfirmasi Hapus -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Konfirmasi Hapus</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin menghapus surat masuk dari <strong id="deleteNama"></strong>?</p>
                    <p class="text-danger"><small><i class="fa fa-warning"></i> Tindakan ini tidak dapat dibatalkan!</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">
                            <i class="fa fa-trash"></i> Hapus
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        // Global function to show delete modal
        function showDeleteModal(id, nama) {
            console.log('showDeleteModal called with ID:', id, 'Nama:', nama);

            // Set modal content
            $('#deleteNama').text(nama);
            $('#deleteForm').attr('action', '<?php echo e(route("desa.surat-masuk.destroy", ":id")); ?>'.replace(':id', id));

            // Show modal
            $('#deleteModal').modal('show');
        }

        $(document).ready(function() {
            console.log('Document ready'); // Debug log

            // Check if jQuery and Bootstrap are loaded
            if (typeof $ === 'undefined') {
                console.error('jQuery is not loaded!');
                return;
            }

            if (typeof $.fn.modal === 'undefined') {
                console.error('Bootstrap modal is not loaded!');
                return;
            }

            // Initialize DataTable with responsive options
            $('.data-table').DataTable({
                responsive: true,
                columnDefs: [{
                        targets: [3],
                        orderable: false
                    } // Disable sorting for action column
                ],
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json'
                }
            });

            // Handle delete button click using event delegation
            $(document).on('click', '.btn-delete', function(e) {
                e.preventDefault();
                console.log('Delete button clicked via event delegation'); // Debug log

                const id = $(this).data('id');
                const nama = $(this).data('nama');

                console.log('ID:', id, 'Nama:', nama); // Debug log

                if (!id || !nama) {
                    console.error('Missing data attributes');
                    alert('Error: Data tidak lengkap');
                    return;
                }

                showDeleteModal(id, nama);
            });

            // Direct click handler as fallback
            $('.btn-delete').on('click', function(e) {
                e.preventDefault();
                console.log('Delete button clicked via direct handler'); // Debug log

                const id = $(this).data('id');
                const nama = $(this).data('nama');

                console.log('ID:', id, 'Nama:', nama); // Debug log

                if (!id || !nama) {
                    console.error('Missing data attributes');
                    alert('Error: Data tidak lengkap');
                    return;
                }

                showDeleteModal(id, nama);
            });

            // Handle form submission with loading state
            $('#deleteForm').on('submit', function() {
                const submitBtn = $(this).find('button[type="submit"]');
                submitBtn.prop('disabled', true);
                submitBtn.html('<i class="fa fa-spinner fa-spin"></i> Menghapus...');
            });

            // Test functions for debugging
            window.testModal = function() {
                console.log('Testing modal...');
                $('#deleteModal').modal('show');
            };

            window.testDeleteModal = function() {
                showDeleteModal(1, 'Test User');
            };

            console.log('All event handlers attached'); // Debug log
        });
    </script>
    <?php $__env->stopPush(); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?><?php /**PATH D:\Hermanto\sudes-ok\resources\views/desa/surat_masuk/index.blade.php ENDPATH**/ ?>