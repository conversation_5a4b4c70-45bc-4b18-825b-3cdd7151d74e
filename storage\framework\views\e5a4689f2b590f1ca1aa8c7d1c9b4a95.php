<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Data Warga <?php $__env->endSlot(); ?>
    <!-- Simple Datatable start -->
    <div class="card-box mb-30">
        <div class="pd-20">
            <h4 class="text-blue h4">Data Warga</h4>
        </div>
        <div class="pb-20">
            <div class="h5 pd-20 mb-0">
                <?php if (isset($component)) { $__componentOriginal5fc1f9c1304bf75cac6bd0db1c43f54b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5fc1f9c1304bf75cac6bd0db1c43f54b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.primary-button','data' => ['class' => 'btn btn-primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button.primary-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'btn btn-primary']); ?><a href="<?php echo e(route('desa.surat-masuk.create')); ?>" style="text-decoration: none; color:white;"> Add Surat Masuk </a> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5fc1f9c1304bf75cac6bd0db1c43f54b)): ?>
<?php $attributes = $__attributesOriginal5fc1f9c1304bf75cac6bd0db1c43f54b; ?>
<?php unset($__attributesOriginal5fc1f9c1304bf75cac6bd0db1c43f54b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5fc1f9c1304bf75cac6bd0db1c43f54b)): ?>
<?php $component = $__componentOriginal5fc1f9c1304bf75cac6bd0db1c43f54b; ?>
<?php unset($__componentOriginal5fc1f9c1304bf75cac6bd0db1c43f54b); ?>
<?php endif; ?>
            </div>
            <table class="data-table table stripe hover nowrap">
                <thead>
                    <tr>
                        <th class="table-plus">#</th>
                        <th>Nama</th>
                        <th>No Surat</th>
                        <th class="datatable-nosort">Aksi</th>
                        <th> </th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $suratMasuk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warga): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td class="table-plus"><?php echo e($loop->iteration); ?></td>
                        <td><?php echo e($warga->nama); ?></td>
                        <td><?php echo e($warga->no_surat); ?></td>
                        <td>
                            <a href="<?php echo e(route('desa.surat-masuk.edit', $warga->id)); ?>"><button-sm class="btn-sm btn-primary"><i class="dw dw-edit2"></i></button-sm></a>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
    <!-- Simple Datatable End -->

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?><?php /**PATH D:\Hermanto\sudes-ok\resources\views/desa/surat_masuk/index.blade.php ENDPATH**/ ?>